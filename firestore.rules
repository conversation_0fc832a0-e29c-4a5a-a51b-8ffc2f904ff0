rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {
  
  
      match /users/{userId} {
      allow read: if true;
      allow write: if request.auth != null;
      
      // Allow querying by barangayCode and lastActive
      allow list: if true;
    }
  
  
    // Helper functions
    function isSignedIn() {
      return request.auth != null;
    }

    function getUserData(userId) {
      return get(/databases/$(database)/documents/users/$(userId)).data;
    }

    function getUserCommunityId() {
      return getUserData(request.auth.uid).communityId;
    }

    function isSuperAdmin() {
      return isSignedIn() &&
             exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
             getUserData(request.auth.uid).role == 'super_admin';
    }

    function isAdmin() {
      return isSignedIn() &&
             exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
             getUserData(request.auth.uid).role == 'admin';
    }

    function canAccessStats() {
      return isAdmin() || isSuperAdmin();
    }

    // Report rules
    match /reports/{reportId} {
      function isAuthor() {
        return request.auth.uid == resource.data.userId;
      }

      function isValidReport() {
        let report = request.resource.data;
        return report.issueType is string &&
               report.issueType.size() > 0 &&
               report.description is string &&
               report.description.size() > 0 &&
               report.address is string &&
               report.address.size() > 0 &&
               report.location is map &&
               report.photoUrls is list &&
               report.status in ['pending', 'under_review', 'in_progress', 'resolved', 'rejected'] &&
               report.communityId == getUserCommunityId();
      }

      // Create: User must be signed in and report must be for their community
      allow create: if isSignedIn() &&
                   request.resource.data.communityId == getUserCommunityId() &&
                   isValidReport();

      // Read: User must be signed in and report must be from their community
      allow read: if isSignedIn() &&
                 resource.data.communityId == getUserCommunityId();

      // Update: Only admin or report creator can update
      allow update: if isSignedIn() &&
                   (isAdmin() || isAuthor()) &&
                   (request.resource.data.diff(resource.data).affectedKeys()
                    .hasOnly(['status', 'updatedAt']) || isAdmin());

      // Delete: Only admin can delete
      allow delete: if isSignedIn() && isAdmin();
    }

    // Admin requests collection
    match /admin_requests/{requestId} {
      allow read: if canAccessStats();
      allow create: if isSignedIn();  // Anyone can submit a request
      allow update, delete: if isSuperAdmin();
    }

    // Communities collection
    match /communities/{communityId} {
      allow read: if isSignedIn();
      allow write: if canAccessStats();
    }

    // Users collection
    match /users/{userId} {
      // Anyone can read user data for email verification during registration
      allow read: if true;

      // Users can create their own documents
      allow create: if isSignedIn() && request.auth.uid == userId;

      // Users can update their own documents, admins and super admins can update any user
      allow update: if isSignedIn() && (
        request.auth.uid == userId ||
        isSuperAdmin() ||
        isAdmin()
      );

      // Only super admins can delete users
      allow delete: if isSuperAdmin();
    }

    // Market items collection rules
    match /market_items/{itemId} {
      allow read: if isSignedIn();

      allow create: if isSignedIn() &&
                   request.resource.data.sellerId == request.auth.uid;

      // Allow admins to update any market item
      allow update: if isSignedIn() && (
        // Seller can update their own items
        resource.data.sellerId == request.auth.uid ||
        // Buyer/seller can update chat-related fields
        (
          request.resource.data.diff(resource.data).affectedKeys()
            .hasOnly(['buyerUnreadCount', 'sellerUnreadCount', 'buyerId']) &&
          (
            resource.data.buyerId == request.auth.uid ||
            request.resource.data.buyerId == request.auth.uid ||
            resource.data.sellerId == request.auth.uid
          )
        ) ||
        // Seller can mark item as sold
        (
          request.resource.data.diff(resource.data).affectedKeys().hasOnly(['isSold', 'soldAt']) &&
          resource.data.sellerId == request.auth.uid &&
          request.resource.data.isSold == true
        ) ||
        // Any user can update soldAt for sold items in their community
        (
          request.resource.data.diff(resource.data).affectedKeys().hasOnly(['soldAt']) &&
          resource.data.isSold == true &&
          resource.data.communityId == getUserCommunityId()
        ) ||
        // Admins can update any item
        canAccessStats()
      );

      allow delete: if isSignedIn() && (
                   resource.data.sellerId == request.auth.uid ||
                   canAccessStats()
                   );
    }

    // Volunteer posts collection rules
    match /volunteer_posts/{postId} {
      allow read: if isSignedIn();

      allow create: if isSignedIn() && (
                   request.resource.data.userId == request.auth.uid ||
                   canAccessStats()
                   );

      allow update: if isSignedIn() && (
                   resource.data.userId == request.auth.uid ||
                   canAccessStats() ||
                   (
                     request.resource.data.diff(resource.data).affectedKeys().hasOnly(['joinedUsers']) &&
                     (
                       (
                         request.resource.data.joinedUsers.hasAll(resource.data.joinedUsers) &&
                         request.resource.data.joinedUsers.removeAll(resource.data.joinedUsers).hasOnly([request.auth.uid])
                       ) ||
                       (
                         resource.data.joinedUsers.hasAll(request.resource.data.joinedUsers) &&
                         resource.data.joinedUsers.removeAll(request.resource.data.joinedUsers).hasOnly([request.auth.uid])
                       )
                     )
                   )
                   );

      allow delete: if isSignedIn() && (
                   resource.data.userId == request.auth.uid ||
                   canAccessStats()
                   );
    }

    // Audit logs collection
    match /audit_logs/{logId} {
      allow read: if canAccessStats();
      allow create: if isSignedIn(); // Any authenticated user can create audit logs
      allow update, delete: if false; // Audit logs should never be modified or deleted
    }

    // Seller ratings collection
    match /seller_ratings/{ratingId} {
      allow read: if isSignedIn();
      allow create: if isSignedIn();
      allow update: if isSignedIn() && request.auth.uid == resource.data.buyerId;
      allow delete: if isSignedIn() && request.auth.uid == resource.data.buyerId;
    }

    // Collection access for statistics
    match /market_items/{document=**} {
      allow read: if canAccessStats();
    }

    match /volunteer_posts/{document=**} {
      allow read: if canAccessStats();
    }

    match /community_notices/{document=**} {
      allow read: if canAccessStats();
    }

    match /chats/{document=**} {
      allow read: if canAccessStats();
    }

    match /reports/{document=**} {
      allow read: if canAccessStats();
    }

    match /seller_ratings/{document=**} {
      allow read: if canAccessStats();
    }

    // User tokens collection for analytics
    match /user_tokens/{document=**} {
      allow read: if canAccessStats();
    }

    // Old Notifications collection (for backward compatibility)
    match /notifications/{notificationId} {
      allow read: if isSignedIn() && resource.data.userId == request.auth.uid;
      allow create: if isSignedIn() && request.resource.data.userId == request.auth.uid;
      allow update: if isSignedIn() && resource.data.userId == request.auth.uid;
      allow delete: if isSignedIn() && resource.data.userId == request.auth.uid;
    }

    // New Notification Status collection
    match /notification_status/{statusId} {
      allow read: if isSignedIn() && resource.data.userId == request.auth.uid;
      allow create: if isSignedIn() && request.resource.data.userId == request.auth.uid;
      allow update: if isSignedIn() && resource.data.userId == request.auth.uid;
      allow delete: if isSignedIn() && resource.data.userId == request.auth.uid;
    }

    // User Notifications collection
    match /user_notifications/{notificationId} {
      allow read: if isSignedIn();
      allow create: if isSignedIn();
      allow update: if false; // Notifications should not be updated
      allow delete: if false; // Notifications should not be deleted
    }

    // Community Notifications collection
    match /community_notifications/{notificationId} {
      allow read: if isSignedIn();
      allow create: if isSignedIn();
      allow update: if false; // Notifications should not be updated
      allow delete: if false; // Notifications should not be deleted
    }

    // User tokens collection
    match /user_tokens/{userId} {
      allow read: if isSignedIn() && (userId == request.auth.uid || canAccessStats());
      allow create: if isSignedIn() && userId == request.auth.uid;
      allow update: if isSignedIn() && userId == request.auth.uid;
      allow delete: if isSignedIn() && userId == request.auth.uid;
    }

    // <<< START OF NEW RULE >>>
    // Rule for accessing the 'posts' collection for analytics
    match /posts/{document=**} {
      allow read: if canAccessStats();
    }
    // <<< END OF NEW RULE >>>

    // Deny access to other collections by default
    // This should be the last rule in this block
    match /{document=**} {
      allow read, write: if false;
    }
  }
}