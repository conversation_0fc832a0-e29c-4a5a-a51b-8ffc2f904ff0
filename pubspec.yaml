name: PULSE
description: "A community engagement mobile application."
publish_to: 'none'
version: 1.0.5+5

environment:
  sdk: ^3.5.4

dependencies:
  flutter:
    sdk: flutter
  cupertino_icons: ^1.0.2
  image_picker: ^1.0.4
  image_cropper: ^9.1.0
  flutter_image_compress: ^2.1.0
  path_provider: ^2.1.2
  shared_preferences: ^2.2.2
  intl: ^0.18.1
  http: ^1.1.0
  dropdown_search: ^5.0.6
  firebase_core: ^2.32.0
  firebase_auth: ^4.17.5
  cloud_firestore: ^4.15.5
  firebase_database: ^10.4.6
  firebase_storage: ^11.7.7
  path: ^1.8.0
  flutter_web_plugins:
    sdk: flutter
  photo_view: ^0.14.0
  mailer: ^6.0.1
  pin_code_fields: ^8.0.1
  crypto: ^3.0.3
  cloud_functions: ^4.7.6
  js: ^0.7.1
  file_picker: ^8.0.7
  flutter_map: ^6.1.0
  latlong2: ^0.9.0
  cloudinary_public: ^0.23.1
  url_launcher: ^6.2.4
  timeago: ^3.6.1
  transparent_image: ^2.0.1
  cached_network_image: ^3.4.0
  pull_to_refresh: ^2.0.0
  geocoding: ^2.1.1
  geolocator: ^10.1.0
  percent_indicator: ^4.2.3
  qr_flutter: ^4.1.0
  uuid: ^4.0.0
  mobile_scanner: ^6.0.7
  video_compress: ^3.1.2
  video_player: ^2.9.5
  chewie: ^1.7.5
  dio: ^5.4.0
  open_filex: ^4.7.0
  permission_handler: ^11.3.0
  gal: ^2.3.1
  image: ^4.0.17
  flutter_pdfview: ^1.3.2
  docx_viewer: ^0.2.1
  video_thumbnail: ^0.5.3
  shimmer: ^3.0.0
  fl_chart: ^0.66.0
  connectivity_plus: ^6.1.4
  firebase_messaging: ^14.7.10
  flutter_local_notifications: ^19.1.0
  pdf: ^3.10.7
  printing: ^5.13.1

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^4.0.0
  flutter_launcher_icons: ^0.13.1

flutter_launcher_icons:
  android: true
  ios: true
  macos: true
  image_path: "assets/icon/pulse_logo.png"
  min_sdk_android: 21
  remove_alpha_ios: true
  adaptive_icon_background: "#FFFFFF"
  adaptive_icon_foreground: "assets/icon/pulse_logo_foreground.png"
  web:
    generate: true
    image_path: "assets/icon/pulse_logo.png"
  windows:
    generate: true
    image_path: "assets/icon/pulse_logo.png"

flutter:
  assets:
    - assets/icon/
  uses-material-design: true
