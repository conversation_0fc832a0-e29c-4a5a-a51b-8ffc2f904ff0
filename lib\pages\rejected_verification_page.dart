import 'package:flutter/material.dart';
import '../services/auth_service.dart';

class RejectedVerificationPage extends StatelessWidget {
  final String registrationId;
  final String? rejectionReason;

  const RejectedVerificationPage({
    Key? key,
    required this.registrationId,
    this.rejectionReason,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text("Account Verification"),
        automaticallyImplyLeading: false,
        actions: [
          IconButton(
            icon: const Icon(Icons.logout),
            onPressed: () async {
              final authService = AuthService();
              try {
                debugPrint('RejectedVerificationPage: Starting logout process with AuthService');

                // Add a 2.5 second delay to show any loading indicators
                await Future.delayed(const Duration(milliseconds: 2500));

                // Navigate after the delay
                if (context.mounted) {
                  Navigator.of(context).pushReplacementNamed('/login');
                }

                // Then sign out after navigation
                await authService.signOut();
                debugPrint('RejectedVerificationPage: Logout completed successfully');
              } catch (e) {
                debugPrint('RejectedVerificationPage: Error during logout: $e');
                if (context.mounted) {
                  Navigator.of(context).pushReplacementNamed('/login');
                }
              }
            },
            tooltip: 'Sign out',
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.cancel_outlined,
              size: 80,
              color: Colors.red,
            ),
            const SizedBox(height: 24),
            const Text(
              'Verification Rejected',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.red,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            const Text(
              'Your account verification has been rejected. Please contact your barangay office for more information.',
              style: TextStyle(fontSize: 16),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            if (rejectionReason != null && rejectionReason!.isNotEmpty)
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.red.shade50,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.red.shade200),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Reason for rejection:',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      rejectionReason!,
                      style: const TextStyle(fontSize: 15),
                    ),
                  ],
                ),
              ),
            const SizedBox(height: 24),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: const Color(0xFFF8F9FA),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.grey.shade300),
              ),
              child: Column(
                children: [
                  const Text(
                    'What to do next:',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 12),
                  _buildStep(
                      1, 'Visit your barangay office during working hours'),
                  _buildStep(2, 'Bring your valid ID and proof of residence'),
                  _buildStep(3, 'Speak with the barangay admin about your application'),
                  _buildStep(
                      4, 'You may need to register again with correct information'),
                ],
              ),
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: () async {
                final authService = AuthService();
                try {
                  debugPrint('RejectedVerificationPage: Starting logout process with AuthService');

                  // Add a 2.5 second delay to show any loading indicators
                  await Future.delayed(const Duration(milliseconds: 2500));

                  // Navigate after the delay
                  if (context.mounted) {
                    Navigator.of(context).pushReplacementNamed('/login');
                  }

                  // Then sign out after navigation
                  await authService.signOut();
                  debugPrint('RejectedVerificationPage: Logout completed successfully');
                } catch (e) {
                  debugPrint('RejectedVerificationPage: Error during logout: $e');
                  if (context.mounted) {
                    Navigator.of(context).pushReplacementNamed('/login');
                  }
                }
              },
              icon: const Icon(Icons.logout, color: Colors.white),
              label: const Text(
                'Sign Out',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.redAccent,
                padding: const EdgeInsets.symmetric(
                  horizontal: 32,
                  vertical: 12,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
            const SizedBox(height: 16),
            TextButton(
              onPressed: () {
                Navigator.of(context).pushReplacementNamed('/register');
              },
              child: const Text(
                'Register Again',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF00C49A),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Helper to build steps
  Widget _buildStep(int number, String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 24,
            height: 24,
            margin: const EdgeInsets.only(right: 12),
            decoration: const BoxDecoration(
              color: Colors.red,
              shape: BoxShape.circle,
            ),
            child: Center(
              child: Text(
                number.toString(),
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          Expanded(
            child: Text(
              text,
              style: const TextStyle(fontSize: 14),
            ),
          ),
        ],
      ),
    );
  }
}
