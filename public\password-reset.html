<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset Your Password - PULSE</title>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #00C49A;
            --error-color: #e53935;
            --success-color: #4caf50;
            --text-color: #333333;
            --light-gray: #f5f5f5;
            --border-color: #e0e0e0;
            --disabled-color: #cccccc;
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Roboto', sans-serif;
            background-color: var(--light-gray);
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            color: var(--text-color);
        }

        .container {
            background-color: white;
            width: 100%;
            max-width: 450px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0,0,0,0.12);
        }

        .header {
            background-color: var(--primary-color);
            color: white;
            padding: 16px;
            text-align: center;
        }

        .header h1 {
            margin: 0;
            font-size: 20px;
            font-weight: 500;
        }

        .content {
            padding: 20px;
        }

        .form-group {
            margin-bottom: 16px;
        }

        label {
            display: block;
            margin-bottom: 6px;
            font-weight: 500;
            font-size: 14px;
        }

        .password-toggle {
            position: relative;
        }

        input[type="text"] {
            width: 100%;
            padding: 12px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            font-size: 16px;
            transition: border-color 0.2s;
        }

        input[type="password"] {
            width: 100%;
            padding: 12px 40px 12px 12px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            font-size: 16px;
            transition: border-color 0.2s;
        }

        input[type="text"]:focus,
        input[type="password"]:focus {
            outline: none;
            border-color: var(--primary-color);
        }

        .toggle-icon {
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            cursor: pointer;
            color: #757575;
            background: none;
            border: none;
            padding: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 24px;
            height: 24px;
            z-index: 2;
        }

        .error-message {
            color: var(--error-color);
            font-size: 12px;
            margin-top: 4px;
            display: none;
        }

        .success-message {
            background-color: #e8f5e9;
            border: 1px solid #a5d6a7;
            color: #2e7d32;
            padding: 16px;
            border-radius: 4px;
            margin-bottom: 16px;
            display: none;
        }

        .password-requirements {
            margin-top: 8px;
            margin-bottom: 16px;
            padding: 12px;
            background-color: var(--light-gray);
            border-radius: 4px;
            display: none;
            opacity: 0;
            transition: opacity 0.3s ease-in-out;
        }

        .password-requirements.visible {
            display: block;
            opacity: 1;
        }

        .password-requirements h3 {
            font-size: 14px;
            margin-bottom: 8px;
            font-weight: 500;
        }

        .requirement {
            display: flex;
            align-items: center;
            margin-bottom: 4px;
            font-size: 13px;
            color: #757575;
            padding: 4px 0;
        }

        .requirement.met {
            color: #333333;
        }

        .requirement-icon {
            margin-right: 8px;
            font-size: 16px;
            color: #757575;
        }

        .requirement.met .requirement-icon {
            color: var(--primary-color);
        }

        button {
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 4px;
            padding: 14px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            width: 100%;
            transition: background-color 0.2s;
        }

        button:hover {
            background-color: #00b38b;
        }

        button:disabled {
            background-color: var(--disabled-color);
            cursor: not-allowed;
        }


        .footer {
            text-align: center;
            padding: 12px;
            background-color: var(--light-gray);
            color: #757575;
            font-size: 12px;
        }

        .spinner {
            border: 2px solid rgba(0, 0, 0, 0.1);
            border-radius: 50%;
            border-top: 2px solid white;
            width: 16px;
            height: 16px;
            animation: spin 1s linear infinite;
            margin: 0 auto;
            display: none;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Responsive adjustments */
        @media (max-width: 480px) {
            .container {
                max-width: 100%;
                height: 100vh;
                display: flex;
                flex-direction: column;
            }

            .content {
                flex: 1;
                overflow-y: auto;
            }

            body {
                background-color: white;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Reset Your Password</h1>
        </div>
        <div class="content">
            <div id="success-message" class="success-message">
                Your password has been reset successfully! You can now log in with your new password.
            </div>

            <div id="reset-form">
                <div class="form-group">
                    <label for="email">Email</label>
                    <input type="text" id="email" readonly>
                </div>

                <div class="form-group">
                    <label for="new-password">New Password</label>
                    <div class="password-toggle">
                        <input type="password" id="new-password" placeholder="Enter your new password">
                        <button type="button" class="toggle-icon" id="toggle-password">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"></path>
                                <line x1="1" y1="1" x2="23" y2="23"></line>
                            </svg>
                        </button>
                    </div>
                    <div class="error-message" id="password-error">Password does not meet requirements</div>
                </div>

                <div id="password-requirements" class="password-requirements">
                    <h3>Password Requirements</h3>
                    <div class="requirement" id="req-length">
                        <span class="requirement-icon">⚪</span>
                        <span>At least 8 characters</span>
                    </div>
                    <div class="requirement" id="req-uppercase">
                        <span class="requirement-icon">⚪</span>
                        <span>Contains uppercase letter</span>
                    </div>
                    <div class="requirement" id="req-lowercase">
                        <span class="requirement-icon">⚪</span>
                        <span>Contains lowercase letter</span>
                    </div>
                    <div class="requirement" id="req-number">
                        <span class="requirement-icon">⚪</span>
                        <span>Contains number</span>
                    </div>
                    <div class="requirement" id="req-special">
                        <span class="requirement-icon">⚪</span>
                        <span>Contains special character</span>
                    </div>
                </div>

                <div class="form-group">
                    <label for="confirm-password">Confirm Password</label>
                    <div class="password-toggle">
                        <input type="password" id="confirm-password" placeholder="Confirm your new password">
                        <button type="button" class="toggle-icon" id="toggle-confirm">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"></path>
                                <line x1="1" y1="1" x2="23" y2="23"></line>
                            </svg>
                        </button>
                    </div>
                    <div class="error-message" id="confirm-error">Passwords do not match</div>
                </div>

                <div class="error-message" id="general-error"></div>

                <button id="reset-button" disabled>
                    <span id="button-text">Reset Password</span>
                    <div id="spinner" class="spinner"></div>
                </button>


            </div>
        </div>
        <div class="footer">
            © 2025 DGMT. All rights reserved.
        </div>
    </div>

    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/9.6.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.6.0/firebase-auth-compat.js"></script>

    <script>
        // Initialize Firebase
        const firebaseConfig = {
            apiKey: "AIzaSyB7NBudQ89T9KFrSYonzmvotE9N-xC-d-U",
            authDomain: "pulse-app-ea5be.firebaseapp.com",
            databaseURL: "https://pulse-app-ea5be-default-rtdb.asia-southeast1.firebasedatabase.app",
            projectId: "pulse-app-ea5be",
            storageBucket: "pulse-app-ea5be.firebasestorage.app",
            messagingSenderId: "46357625509",
            appId: "1:46357625509:web:0f04a6754ccac99eb22b4c"
        };

        firebase.initializeApp(firebaseConfig);

        // Get elements
        const emailField = document.getElementById('email');
        const newPasswordField = document.getElementById('new-password');
        const confirmPasswordField = document.getElementById('confirm-password');
        const resetButton = document.getElementById('reset-button');
        const passwordError = document.getElementById('password-error');
        const confirmError = document.getElementById('confirm-error');
        const generalError = document.getElementById('general-error');
        const successMessage = document.getElementById('success-message');
        const resetForm = document.getElementById('reset-form');
        const buttonText = document.getElementById('button-text');
        const spinner = document.getElementById('spinner');
        const togglePassword = document.getElementById('toggle-password');
        const toggleConfirm = document.getElementById('toggle-confirm');

        // Get action code from URL
        const urlParams = new URLSearchParams(window.location.search);
        const actionCode = urlParams.get('oobCode');

        // Toggle password visibility
        togglePassword.addEventListener('click', () => {
            if (newPasswordField.type === 'password') {
                newPasswordField.type = 'text';
                togglePassword.innerHTML = `
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                        <circle cx="12" cy="12" r="3"></circle>
                    </svg>
                `;
            } else {
                newPasswordField.type = 'password';
                togglePassword.innerHTML = `
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"></path>
                        <line x1="1" y1="1" x2="23" y2="23"></line>
                    </svg>
                `;
            }
        });

        toggleConfirm.addEventListener('click', () => {
            if (confirmPasswordField.type === 'password') {
                confirmPasswordField.type = 'text';
                toggleConfirm.innerHTML = `
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                        <circle cx="12" cy="12" r="3"></circle>
                    </svg>
                `;
            } else {
                confirmPasswordField.type = 'password';
                toggleConfirm.innerHTML = `
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"></path>
                        <line x1="1" y1="1" x2="23" y2="23"></line>
                    </svg>
                `;
            }
        });

        // Validate the action code and get email
        if (actionCode) {
            firebase.auth().verifyPasswordResetCode(actionCode)
                .then((email) => {
                    emailField.value = email;
                })
                .catch((error) => {
                    generalError.textContent = 'Invalid or expired password reset link. Please request a new one.';
                    generalError.style.display = 'block';
                    resetButton.disabled = true;
                });
        } else {
            generalError.textContent = 'No reset code found. Please request a password reset from the login page.';
            generalError.style.display = 'block';
            resetButton.disabled = true;
        }

        // Password requirement elements
        const reqLength = document.getElementById('req-length');
        const reqUppercase = document.getElementById('req-uppercase');
        const reqLowercase = document.getElementById('req-lowercase');
        const reqNumber = document.getElementById('req-number');
        const reqSpecial = document.getElementById('req-special');

        // Check password requirements
        function checkPasswordRequirements(password) {
            const hasLength = password.length >= 8;
            const hasUppercase = /[A-Z]/.test(password);
            const hasLowercase = /[a-z]/.test(password);
            const hasNumber = /[0-9]/.test(password);
            const hasSpecial = /[!@#$%^&*(),.?":{}|<>]/.test(password);

            // Update UI for each requirement
            updateRequirement(reqLength, hasLength);
            updateRequirement(reqUppercase, hasUppercase);
            updateRequirement(reqLowercase, hasLowercase);
            updateRequirement(reqNumber, hasNumber);
            updateRequirement(reqSpecial, hasSpecial);

            return hasLength && hasUppercase && hasLowercase && hasNumber && hasSpecial;
        }

        function updateRequirement(element, isMet) {
            if (isMet) {
                element.classList.add('met');
                element.querySelector('.requirement-icon').textContent = '✓';
            } else {
                element.classList.remove('met');
                element.querySelector('.requirement-icon').textContent = '⚪';
            }
        }

        // Form validation
        const passwordRequirements = document.getElementById('password-requirements');

        newPasswordField.addEventListener('focus', () => {
            passwordRequirements.classList.add('visible');
        });

        newPasswordField.addEventListener('blur', () => {
            if (newPasswordField.value.length === 0) {
                passwordRequirements.classList.remove('visible');
            }
        });

        newPasswordField.addEventListener('input', validateForm);
        confirmPasswordField.addEventListener('input', validateForm);

        function validateForm() {
            const password = newPasswordField.value;
            const confirmPassword = confirmPasswordField.value;

            // Check password requirements
            const meetsRequirements = checkPasswordRequirements(password);

            if (!meetsRequirements) {
                passwordError.style.display = 'block';
            } else {
                passwordError.style.display = 'none';
            }

            // Check if passwords match
            if (password && confirmPassword && password !== confirmPassword) {
                confirmError.style.display = 'block';
            } else {
                confirmError.style.display = 'none';
            }

            // Enable/disable button
            resetButton.disabled = !meetsRequirements || !confirmPassword || password !== confirmPassword;

            return meetsRequirements && confirmPassword && password === confirmPassword;
        }

        // Handle password reset
        resetButton.addEventListener('click', () => {
            if (!validateForm()) return;

            // Show loading state
            buttonText.style.display = 'none';
            spinner.style.display = 'block';
            resetButton.disabled = true;

            const newPassword = newPasswordField.value;

            firebase.auth().confirmPasswordReset(actionCode, newPassword)
                .then(() => {
                    // Password reset successful
                    resetForm.style.display = 'none';
                    successMessage.style.display = 'block';
                })
                .catch((error) => {
                    // Error occurred during confirmation
                    generalError.textContent = error.message;
                    generalError.style.display = 'block';

                    // Reset loading state
                    buttonText.style.display = 'block';
                    spinner.style.display = 'none';
                    resetButton.disabled = false;
                });
        });
    </script>
</body>
</html>
