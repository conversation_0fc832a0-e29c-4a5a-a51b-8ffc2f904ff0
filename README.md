# PulseApp

A community engagement mobile application that helps connect and empower local communities.

## Features

- **Community Notices**: Stay updated with important announcements and events in your community
- **Marketplace**: Connect with local businesses and browse community marketplace
- **Volunteer Opportunities**: Find and participate in local volunteer activities
- **Community Reporting**: Easy-to-use platform for reporting community concerns

## Getting Started

This app is built with Flutter and requires the following prerequisites:

- Flutter SDK (^3.5.4)
- Dart SDK
- Android Studio / Xcode for mobile development

### Installation

1. Clone the repository
2. Install dependencies:
